"""API web para exponer servicios de pruebas y automatizaciu00f3n."""

import os
import sys
import asyncio
import concurrent.futures
from typing import Dict, List, Optional, Any
from datetime import datetime
from dotenv import load_dotenv
import json

from fastapi import FastAPI, HTTPException, Depends, Body, File, UploadFile, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field

# Importar las nuevas rutas modulares
from src.API.project_routes import router as project_router
from src.API.suite_routes import router as suite_router
from src.API.testcase_routes import router as testcase_router
from src.API.prompt_routes import router as prompt_router

# Importar modelos desde el archivo separado
from src.API.models import (
    SmokeTestRequest,
    FullTestRequest,
    GherkinRequest,
    CodeGenerationRequest,
    EnhanceStoryRequest,
    GenerateManualTestsRequest,
    GenerateGherkinRequest,
    SaveHistoryRequest,
    SummarizeRequest
)

# Cargar variables de entorno
load_dotenv()

# Importar el servicio central
from src.Core.test_service import TestService
# Importar el nuevo servicio de prompts
from src.Core.prompt_service import PromptService

# Verificar que la API key estu00e9 configurada
if not os.environ.get("GOOGLE_API_KEY"):
    raise ValueError("No se encontru00f3 la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY.")

# Crear la aplicaciu00f3n FastAPI
app = FastAPI(
    title="QA Agent API",
    description="API para automatizaciu00f3n de pruebas, generaciu00f3n de casos de prueba y gestiou00f3n de proyectos.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configurar CORS para permitir solicitudes desde cualquier origen
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permitir todos los oru00edgenes en desarrollo
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Incluir las rutas modulares
app.include_router(project_router)
app.include_router(suite_router)
app.include_router(testcase_router)
app.include_router(prompt_router)

# Funciou00f3n para obtener una instancia del servicio de pruebas
def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

# Función para obtener una instancia del servicio de prompts
def get_prompt_service():
    """Crea y devuelve una instancia del servicio de prompts."""
    return PromptService()

# Función helper para ejecutar tests en un proceso separado
def run_smoke_test_sync(instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> Dict[str, Any]:
    """Ejecuta un smoke test de manera síncrona en un proceso separado."""
    try:
        # Crear el servicio de pruebas
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

        # Usar el método sincrónico que maneja su propio bucle de eventos
        result = test_service.run_smoke_test(
            instructions=instructions,
            url=url,
            user_story=user_story
        )

        # Asegurar que siempre devolvemos un resultado válido
        if result is None:
            return {
                "success": False,
                "error": "El test no devolvió ningún resultado"
            }
        
        # Función auxiliar para serializar objetos complejos
        def serialize_object(obj):
            """Serializa objetos complejos de manera segura."""
            if obj is None or isinstance(obj, (str, int, float, bool)):
                return obj
            elif isinstance(obj, (list, tuple)):
                return [serialize_object(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: serialize_object(value) for key, value in obj.items()}
            elif hasattr(obj, '__dict__'):
                try:
                    return {key: serialize_object(value) for key, value in obj.__dict__.items()}
                except:
                    return str(obj)
            else:
                return str(obj)

        # Convertir el resultado a un formato serializable
        try:
            serializable_result = serialize_object(result)
        except Exception as serialize_error:
            print(f"Error de serialización personalizada: {str(serialize_error)}")
            # Fallback: intentar serialización básica
            try:
                serializable_result = json.loads(json.dumps(result, default=lambda o: str(o)))
            except Exception as fallback_error:
                print(f"Error de serialización fallback: {str(fallback_error)}")
                # Último recurso: devolver solo la información básica
                serializable_result = {
                    "success": result.get("success", False) if isinstance(result, dict) else False,
                    "error": "Error de serialización en el resultado",
                    "test_id": result.get("test_id") if isinstance(result, dict) else None
                }

        # Si el resultado ya indica éxito/fallo, devolverlo tal como está
        if isinstance(serializable_result, dict) and 'success' in serializable_result:
            return serializable_result

        # Si el resultado no tiene el formato esperado, envolverlo
        return {
            "success": True,
            "result": serializable_result
        }

    except TypeError as e:
        error_msg = f"Error de serialización: {str(e)}"
        print(f"Error en run_smoke_test_sync: {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "test_id": None,
            "history": None,
            "screenshot_paths": [],
            "history_path": None
        }
    except Exception as e:
        error_msg = str(e)
        print(f"Error en run_smoke_test_sync: {error_msg}")
        
        # No re-lanzar la excepción, solo devolver el error
        return {
            "success": False,
            "error": error_msg,
            "test_id": None,
            "history": None,
            "screenshot_paths": [],
            "history_path": None
        }

# Función helper para ejecutar full tests en un proceso separado
def run_full_test_sync(gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
    """Ejecuta un full test de manera síncrona en un proceso separado."""
    try:
        # Crear el servicio de pruebas
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

        # Usar el método sincrónico que maneja su propio bucle de eventos
        result = test_service.run_full_test(
            gherkin_scenario=gherkin_scenario,
            url=url
        )

        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Los modelos Pydantic ahora se importan desde src.API.models

# Rutas de API para pruebas
@app.post("/api/tests/smoke", summary="Ejecutar smoke test")
async def run_smoke_test(request: SmokeTestRequest):
    """Ejecuta un smoke test con las instrucciones proporcionadas."""
    try:
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_smoke_test_sync,
                request.instructions,
                request.url,
                request.user_story
            )

        # Verificar que tengamos un resultado válido
        if result is None:
            return JSONResponse(
                status_code=500,
                content={"error": "No se recibió resultado del test"}
            )
        
        # Si el resultado indica fallo pero se ejecutó correctamente (sin 500)
        if isinstance(result, dict) and result.get('success') is False:
            # Devolver el error pero con código 200 para indicar que la API funcionó
            return JSONResponse(content=result)
        
        # Resultado exitoso
        return JSONResponse(content=result)
        
    except Exception as e:
        error_msg = str(e)
        print(f"Error en endpoint smoke test: {error_msg}")
        
        # Solo devolver 500 si realmente hay un error de la API, no del test
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error interno del servidor: {error_msg}"
            }
        )

@app.post("/api/tests/full", summary="Ejecutar test completo")
async def run_full_test(request: FullTestRequest):
    """Ejecuta un test completo con el escenario Gherkin proporcionado."""
    try:
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_full_test_sync,
                request.gherkin_scenario,
                request.url
            )

        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate/gherkin", summary="Generar escenario Gherkin")
async def create_gherkin_scenario(
    request: GherkinRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera un escenario Gherkin a partir de instrucciones."""
    try:
        from src.Utilities.response_cleaner import clean_gherkin_response

        # Use the new PromptService for Gherkin generation
        # Create context from the request
        context = {
            "instructions": request.instructions,
            "url": request.url,
            "user_story": request.user_story
        }
        
        # Generate test cases first, then Gherkin scenarios
        if request.user_story:
            # If we have a user story, enhance it and generate manual tests first
            enhanced_story = prompt_service.enhance_user_story(
                user_story=request.user_story,
                language=request.language
            )
            manual_tests = prompt_service.generate_manual_test_cases(
                enhanced_story=enhanced_story,
                language=request.language
            )
            gherkin = prompt_service.generate_gherkin(
                test_cases=manual_tests,
                language=request.language,
                **context
            )
        else:
            # For direct instructions, create a basic test case format
            test_cases = f"Test Case: {request.instructions}"
            gherkin = prompt_service.generate_gherkin(
                test_cases=test_cases,
                language=request.language,
                **context
            )

        # Clean the response
        clean_gherkin = clean_gherkin_response(gherkin)

        return {"gherkin": clean_gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate/code", summary="Generar cu00f3digo de automatizaciu00f3n")
async def generate_code(
    request: CodeGenerationRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera cu00f3digo de automatizaciu00f3n para un framework especu00edfico."""
    try:
        # Use the new PromptService for code generation
        code = prompt_service.generate_code(
            framework=request.framework,
            gherkin_scenario=request.gherkin_scenario,
            history=request.test_history
        )
        return {"code": code}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Rutas de API para agentes de historias
@app.post("/api/stories/enhance", summary="Mejorar historia de usuario")
async def enhance_story(
    request: EnhanceStoryRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Mejora una historia de usuario."""
    try:
        from src.Utilities.response_cleaner import clean_user_story_response

        # Use the new PromptService for user story enhancement
        enhanced_story = prompt_service.enhance_user_story(
            user_story=request.user_story,
            language=request.language
        )

        # Clean the response
        clean_story = clean_user_story_response(enhanced_story)

        return {"enhanced_story": clean_story}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stories/generate-manual-tests", summary="Generar casos de prueba manuales")
async def generate_manual_tests(
    request: GenerateManualTestsRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera casos de prueba manuales a partir de una historia mejorada."""
    try:
        import json
        import re

        # Use the new PromptService for manual test generation
        manual_tests = prompt_service.generate_manual_test_cases(
            enhanced_story=request.enhanced_story,
            language=request.language
        )

        # First, try to extract JSON if it's wrapped in code blocks
        json_match = re.search(r'```json\s*(.*?)\s*```', manual_tests, re.DOTALL)
        if json_match:
            json_content = json_match.group(1)
            try:
                test_cases_json = json.loads(json_content)
                return {"manual_tests": test_cases_json}
            except json.JSONDecodeError as e:
                # If JSON parsing fails, return the error details for debugging
                return {"manual_tests": json_content, "error": f"JSON parsing error: {str(e)}"}
        
        # If no JSON code block found, try to parse the whole response as JSON
        try:
            test_cases_json = json.loads(manual_tests)
            return {"manual_tests": test_cases_json}
        except json.JSONDecodeError:
            # If all JSON parsing fails, return as string
            return {"manual_tests": manual_tests}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stories/generate-gherkin", summary="Generar escenarios Gherkin desde casos manuales")
async def generate_gherkin_from_manual(
    request: GenerateGherkinRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera escenarios Gherkin a partir de casos de prueba manuales."""
    try:
        from src.Utilities.response_cleaner import clean_gherkin_response

        # Use the new PromptService for Gherkin generation
        gherkin = prompt_service.generate_gherkin(
            test_cases=request.manual_tests,
            language=request.language
        )

        # Clean the response
        clean_gherkin = clean_gherkin_response(gherkin)

        return {"gherkin": clean_gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/projects/save-history", summary="Guardar historial en proyecto")
async def save_history_to_project(
    request: SaveHistoryRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Guarda un historial de prueba en un proyecto."""
    try:
        test_case = test_service.save_history_to_project(
            project_id=request.project_id,
            suite_id=request.suite_id,
            test_history=request.test_history,
            name=request.name,
            description=request.description,
            gherkin=request.gherkin
        )
        return {"test_case": test_case}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/tests/summarize", summary="Resumir resultados de prueba")
async def summarize_test_results(
    request: SummarizeRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Genera un resumen de los resultados de prueba usando IA."""
    try:
        summary = test_service.summarize_test_results(request.test_results)
        return {"summary": summary}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Endpoint para verificar el estado de la API
@app.get("/api/health", summary="Verificar estado de la API")
async def get_status():
    """Verifica el estado de la API y devuelve informacion basica."""
    return {
        "status": "online",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "api_key_configured": bool(os.environ.get("GOOGLE_API_KEY"))
    }

# Punto de entrada para ejecutar la aplicaciu00f3n
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)
