#!/usr/bin/env python3
"""
Script de prueba para verificar que la serialización de DOMHistoryElement funciona correctamente.
"""

import json
import sys
import os

# Agregar el directorio raíz al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Simular un objeto DOMHistoryElement
class MockDOMHistoryElement:
    def __init__(self, xpath="//button[@id='test']", tag_name="button", attributes=None):
        self.xpath = xpath
        self.tag_name = tag_name
        self.attributes = attributes or {"id": "test", "class": "btn"}
    
    def __str__(self):
        return f"DOMHistoryElement(xpath='{self.xpath}', tag_name='{self.tag_name}', attributes={self.attributes})"

# Simular un historial con objetos DOMHistoryElement
class MockHistory:
    def __init__(self):
        self._model_actions = [
            {
                "action": "click_element",
                "interacted_element": MockDOMHistoryElement(),
                "screenshot": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            },
            {
                "action": "input_text",
                "interacted_element": MockDOMHistoryElement("//input[@name='username']", "input", {"name": "username", "type": "text"}),
                "text": "test_user"
            }
        ]
        self._urls = ["https://example.com"]
        self._action_names = ["Click button", "Input text"]
        self._extracted_content = ["Button clicked successfully", "Text entered"]
        self._errors = []
        self._final_result = "Test completed successfully"
    
    def model_actions(self):
        return self._model_actions
    
    def urls(self):
        return self._urls
    
    def action_names(self):
        return self._action_names
    
    def extracted_content(self):
        return self._extracted_content
    
    def errors(self):
        return self._errors
    
    def final_result(self):
        return self._final_result

def test_serialization():
    """Prueba la función de serialización personalizada."""
    
    # Importar la función de serialización
    try:
        from src.Utilities.test_executor import TestExecutor
        executor = TestExecutor(api_key="test_key")
        
        # Crear un historial mock
        mock_history = MockHistory()
        
        # Probar la serialización de un objeto DOMHistoryElement
        dom_element = MockDOMHistoryElement()
        serialized_element = executor._serialize_history_object(dom_element)
        
        print("✅ Serialización de DOMHistoryElement:")
        print(json.dumps(serialized_element, indent=2, ensure_ascii=False))
        print()
        
        # Probar la serialización de las acciones del modelo
        model_actions = mock_history.model_actions()
        serialized_actions = executor._serialize_history_object(model_actions)
        
        print("✅ Serialización de model_actions:")
        print(json.dumps(serialized_actions, indent=2, ensure_ascii=False))
        print()
        
        # Probar que se puede convertir a JSON sin errores
        json_str = json.dumps(serialized_actions, ensure_ascii=False)
        print("✅ Conversión a JSON exitosa")
        print(f"Tamaño del JSON: {len(json_str)} caracteres")
        
        return True
        
    except Exception as e:
        print(f"❌ Error durante la prueba: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_serialization():
    """Prueba la función de serialización de la API."""
    
    try:
        # Simular un resultado con objetos DOMHistoryElement
        result = {
            "success": True,
            "test_id": "20231201120000",
            "history": {
                "model_actions": [
                    {
                        "action": "click_element",
                        "interacted_element": MockDOMHistoryElement()
                    }
                ]
            }
        }
        
        # Función de serialización de la API
        def serialize_object(obj):
            """Serializa objetos complejos de manera segura."""
            if obj is None or isinstance(obj, (str, int, float, bool)):
                return obj
            elif isinstance(obj, (list, tuple)):
                return [serialize_object(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: serialize_object(value) for key, value in obj.items()}
            elif hasattr(obj, '__dict__'):
                try:
                    return {key: serialize_object(value) for key, value in obj.__dict__.items()}
                except:
                    return str(obj)
            else:
                return str(obj)
        
        # Probar la serialización
        serialized_result = serialize_object(result)
        
        print("✅ Serialización de resultado de API:")
        print(json.dumps(serialized_result, indent=2, ensure_ascii=False))
        
        # Probar que se puede convertir a JSON sin errores
        json_str = json.dumps(serialized_result, ensure_ascii=False)
        print("✅ Conversión a JSON de API exitosa")
        print(f"Tamaño del JSON: {len(json_str)} caracteres")
        
        return True
        
    except Exception as e:
        print(f"❌ Error durante la prueba de API: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Iniciando pruebas de serialización...")
    print("=" * 50)
    
    # Probar serialización del TestExecutor
    print("1. Probando serialización del TestExecutor:")
    test1_success = test_serialization()
    print()
    
    # Probar serialización de la API
    print("2. Probando serialización de la API:")
    test2_success = test_api_serialization()
    print()
    
    # Resumen
    print("=" * 50)
    if test1_success and test2_success:
        print("✅ Todas las pruebas pasaron exitosamente!")
        print("🎉 El problema de serialización de DOMHistoryElement debería estar resuelto.")
    else:
        print("❌ Algunas pruebas fallaron.")
        print("🔧 Revisa los errores anteriores para más detalles.")
